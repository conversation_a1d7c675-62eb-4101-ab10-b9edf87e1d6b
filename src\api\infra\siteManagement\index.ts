import request from '@/config/axios'

// ==================== 场地管理相关类型定义 ====================

// 座位类型配置
export interface SeatType {
  name: string
  count: number
  remark?: string
}

// 场地信息接口
export interface Site {
  id?: number
  name: string
  campus: string
  campusName: string
  type: string
  location: string
  seatTotal?: number
  seatTypes: SeatType[]
  seatDetail?: string
  equipment?: string
  status: string
  description?: string
  manager: string
  managerPhone: string
  createTime?: string
  updateTime?: string
}

// 场地分页查询参数
export interface SitePageParams {
  pageNum: number
  pageSize: number
  campus?: string
  campusName?: string
  type?: string
  status?: string
  capacity?: string
  keyword?: string
}

// 场地分页查询结果
export interface SitePageResult {
  total: number
  list: Site[]
}

// 场地列表查询参数（不分页）
export interface SiteListParams {
  campus?: string
  campusName?: string
  type?: string
  status?: string
  name?: string
}

// 新增场地请求参数
export interface CreateSiteParams {
  name: string
  campus: string
  campusName: string
  type: string
  location: string
  seatTypes: SeatType[]
  equipment?: string
  status: string
  description?: string
  manager: string
  managerPhone: string
}

// 更新场地请求参数
export interface UpdateSiteParams {
  id: number
  name: string
  campus: string
  campusName: string
  type: string
  location: string
  seatTypes: SeatType[]
  equipment?: string
  status: string
  description?: string
  manager: string
  managerPhone: string
}

// 场地统计结果
export interface SiteStatisticsResult {
  totalCount: number
  availableCount: number
  reservedCount: number
  maintenanceCount: number
  disabledCount: number
}

// ==================== 场地预约相关类型定义 ====================

// 场地预约信息接口
export interface SiteAppointment {
  id?: number
  siteId: number
  siteName?: string
  siteCampusName?: string
  activityName: string
  activityType: string
  startDate: string
  endDate: string
  startTime: string
  endTime: string
  timeRange?: string
  peopleCount: number
  contactName: string
  contactPhone: string
  status?: string
  remark?: string
  createTime?: string
  updateTime?: string
}

// 新增预约请求参数
export interface CreateAppointmentParams {
  siteId: number
  activityName: string
  activityType: string
  startDate: string
  endDate: string
  startTime: string
  endTime: string
  peopleCount: number
  contactName: string
  contactPhone: string
  status?: string
  remark?: string
}

// 更新预约请求参数
export interface UpdateAppointmentParams {
  id: number
  siteId: number
  activityName: string
  activityType: string
  startDate: string
  endDate: string
  startTime: string
  endTime: string
  peopleCount: number
  contactName: string
  contactPhone: string
  status?: string
  remark?: string
}

// 预约分页查询参数
export interface AppointmentPageParams {
  pageNum: number
  pageSize: number
  siteId?: number
  activityType?: string
  status?: string
  startDate?: string
  endDate?: string
  contactName?: string
}

// 预约分页查询结果
export interface AppointmentPageResult {
  total: number
  list: SiteAppointment[]
}

// 预约列表查询参数（不分页）
export interface AppointmentListParams {
  siteId?: number
  activityType?: string
  status?: string
  startDate?: string
  endDate?: string
  contactName?: string
}

// 根据场地和日期查询预约参数
export interface AppointmentBySiteAndDateParams {
  siteId: number
  date?: string
  startDate?: string
  endDate?: string
}

// 场地管理API
export const SiteManagementApi = {
  // ==================== 场地管理接口 ====================

  // 场地分页查询
  getSitePage: async (params: SitePageParams): Promise<SitePageResult> => {
    return await request.get({ url: '/publicbiz/site-management/page', params })
  },

  // 场地列表查询（不分页）
  getSiteList: async (params?: SiteListParams): Promise<Site[]> => {
    return await request.get({ url: '/publicbiz/site-management/list', params })
  },

  // 新增场地
  createSite: async (data: CreateSiteParams): Promise<{ id: number }> => {
    return await request.post({ url: '/publicbiz/site-management/create', data })
  },

  // 编辑场地
  updateSite: async (data: UpdateSiteParams): Promise<boolean> => {
    return await request.put({ url: '/publicbiz/site-management/update', data })
  },

  // 删除场地
  deleteSite: async (id: number): Promise<boolean> => {
    return await request.delete({ url: `/publicbiz/site-management/delete/${id}` })
  },

  // 场地详情查询
  getSiteDetail: async (id: number): Promise<Site> => {
    return await request.get({ url: `/publicbiz/site-management/detail/${id}` })
  },

  // 场地统计报表
  getSiteStatistics: async (params?: {
    campus?: string
    campusName?: string
  }): Promise<SiteStatisticsResult> => {
    return await request.get({ url: '/publicbiz/site-management/statistics', params })
  },

  // ==================== 场地预约接口 ====================

  // 新增预约
  createAppointment: async (data: CreateAppointmentParams): Promise<{ id: number }> => {
    return await request.post({ url: '/publicbiz/site-management/appointment/create', data })
  },

  // 更新预约
  updateAppointment: async (data: UpdateAppointmentParams): Promise<boolean> => {
    return await request.put({ url: '/publicbiz/site-management/appointment/update', data })
  },

  // 预约分页查询
  getAppointmentPage: async (params: AppointmentPageParams): Promise<AppointmentPageResult> => {
    return await request.get({ url: '/publicbiz/site-management/appointment/page', params })
  },

  // 预约详情查询
  getAppointmentDetail: async (id: number): Promise<SiteAppointment> => {
    return await request.get({ url: `/publicbiz/site-management/appointment/detail/${id}` })
  },

  // 取消预约
  cancelAppointment: async (id: number): Promise<boolean> => {
    return await request.put({ url: `/publicbiz/site-management/appointment/cancel/${id}` })
  },

  // 根据场地和日期查询预约
  getAppointmentBySiteAndDate: async (
    params: AppointmentBySiteAndDateParams
  ): Promise<SiteAppointment[]> => {
    return await request.get({
      url: '/publicbiz/site-management/appointment/listBySiteAndDate',
      params
    })
  },

  // 预约列表查询（不分页）
  getAppointmentList: async (params?: AppointmentListParams): Promise<SiteAppointment[]> => {
    return await request.get({ url: '/publicbiz/site-management/appointment/list', params })
  }
}

export default SiteManagementApi
