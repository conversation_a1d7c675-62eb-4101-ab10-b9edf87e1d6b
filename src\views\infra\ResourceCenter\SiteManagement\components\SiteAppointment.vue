<!--
  页面名称：预约场地抽屉
  功能描述：填写活动信息，直接预约场地，展示场地信息和已预约信息，支持表单校验、提交、取消，样式自适应
-->
<template>
  <el-drawer
    v-model="visible"
    :title="
      props.editMode ? `编辑预约 - ${siteInfo.name || ''}` : `直接预约场地 - ${siteInfo.name || ''}`
    "
    size="700px"
    direction="rtl"
    :with-header="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="onCancel"
    class="site-appointment-drawer"
  >
    <!-- 场地信息卡片 -->
    <el-card class="site-info-card" shadow="never">
      <div class="site-title">{{ siteInfo.name }}</div>
      <el-row :gutter="16" class="site-info-row">
        <el-col :sm="6" :xs="12">校区：{{ siteInfo.campusName || siteInfo.campus }}</el-col>
        <el-col :sm="6" :xs="12">类型：{{ siteInfo.type }}</el-col>
        <el-col :sm="6" :xs="12">容量：{{ siteInfo.seatTotal || 0 }}座</el-col>
        <el-col :sm="6" :xs="12">位置：{{ siteInfo.location }}</el-col>
      </el-row>
      <el-row :gutter="16" class="site-info-row">
        <el-col :sm="18" :xs="24">设备：{{ siteInfo.equipment }}</el-col>
        <el-col :sm="6" :xs="24">
          状态：<el-tag type="success" v-if="siteInfo.status === '可用'">可用</el-tag>
          <el-tag type="warning" v-else-if="siteInfo.status === '已预约'">已预约</el-tag>
          <el-tag type="info" v-else-if="siteInfo.status === '维护中'">维护中</el-tag>
          <el-tag type="danger" v-else-if="siteInfo.status === '停用'">停用</el-tag>
        </el-col>
      </el-row>
    </el-card>
    <!-- 绿色提示 -->
    <el-alert type="success" show-icon :closable="false" class="mb-2" style="margin-top: 12px">
      直接预约：填写信息后点击“立即预约”即可直接预约成功，无需等待确认。
    </el-alert>
    <!-- 预约表单 -->
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="110px"
      label-position="top"
      class="site-appointment-form"
    >
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="活动名称" prop="activityName" required>
            <el-input v-model="form.activityName" placeholder="请输入活动名称" maxlength="30" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="活动类型" prop="activityType" required>
            <el-select v-model="form.activityType" placeholder="请选择活动类型">
              <el-option
                v-for="item in activityTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="开始日期" prop="startDate" required>
            <el-date-picker
              v-model="form.startDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="请选择开始日期"
              style="width: 100%"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="结束日期" prop="endDate" required>
            <el-date-picker
              v-model="form.endDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="请选择结束日期"
              style="width: 100%"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="开始时间" prop="startTime" required>
            <el-time-picker
              v-model="form.startTime"
              placeholder="请选择开始时间"
              format="HH:mm"
              value-format="HH:mm"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="结束时间" prop="endTime" required>
            <el-time-picker
              v-model="form.endTime"
              placeholder="请选择结束时间"
              format="HH:mm"
              value-format="HH:mm"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="预计人数" prop="peopleCount" required>
            <el-input v-model="form.peopleCount" placeholder="请输入预计人数" maxlength="5" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="联系人" prop="contactName" required>
            <el-input v-model="form.contactName" placeholder="请输入联系人姓名" maxlength="10" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="联系电话" prop="contactPhone" required>
            <el-input v-model="form.contactPhone" placeholder="请输入联系电话" maxlength="20" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="预约状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择预约状态" style="width: 100%">
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="2"
          placeholder="请输入备注信息（可选）"
          maxlength="100"
        />
      </el-form-item>
    </el-form>
    <!-- 已有预约信息 -->
    <div class="reserved-list">
      <div class="reserved-title">该场地已有预约</div>

      <!-- 加载状态 -->
      <div v-if="reservationsLoading || !apiDataFetched" style="text-align: center; padding: 20px">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span style="margin-left: 8px">正在加载预约信息...</span>
      </div>

      <!-- 无预约数据 -->
      <div
        v-else-if="apiDataFetched && filteredReservations.length === 0"
        style="text-align: center; padding: 20px; color: #999"
      >
        暂无有效预约记录
        <!-- 调试信息（生产环境可删除） -->
        <div style="font-size: 12px; margin-top: 8px; color: #ccc">
          调试：API已获取={{ apiDataFetched }}, 原始数据={{ apiReservations.length }}条, 过滤后={{
            filteredReservations.length
          }}条
        </div>
      </div>

      <!-- 预约列表 -->
      <div v-else>
        <div style="margin-bottom: 10px; color: #666; font-size: 14px">
          共 {{ filteredReservations.length }} 条有效预约
        </div>
        <el-card
          v-for="(item, idx) in filteredReservations"
          :key="item.id || idx"
          class="reserved-card"
          shadow="never"
        >
          <div class="reserved-activity">{{ item.activityName }}</div>
          <div class="reserved-info"> 时间：{{ formatDateRange(item) }} </div>
          <div class="reserved-info">
            类型：{{ item.activityType }} | 人数：{{ item.peopleCount }} | 负责人：{{
              item.contactName
            }}（{{ item.contactPhone }}）
          </div>
          <div class="reserved-info">备注：{{ item.remark || '无' }}</div>
          <el-tag :type="getStatusTagType(item.status)" class="reserved-status">
            {{ item.status }}
          </el-tag>
        </el-card>
      </div>
    </div>
    <!-- 底部按钮 -->
    <div style="text-align: right; margin-top: 16px">
      <el-button @click="onCancel" :disabled="loading">取消</el-button>
      <el-button type="primary" @click="onSubmit" :loading="loading">
        {{ props.editMode ? '保存修改' : '立即预约' }}
      </el-button>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import {
  SiteManagementApi,
  type Site,
  type SiteAppointment,
  type CreateAppointmentParams,
  type UpdateAppointmentParams,
  type AppointmentListParams
} from '@/api/infra/siteManagement'

const props = defineProps<{
  visible: boolean
  siteInfo: Site
  reservedList?: SiteAppointment[]
  editMode?: boolean
  editData?: SiteAppointment
}>()
const emit = defineEmits<{
  close: []
  submit: []
}>()

const visible = ref(props.visible)
watch(
  () => props.visible,
  (v) => {
    visible.value = v
    // 当弹窗打开时，根据模式初始化表单
    if (v) {
      if (props.editMode && props.editData) {
        initFormForEdit()
      } else if (!props.editMode) {
        resetForm()
      }
    }
  }
)

// 监听编辑数据变化，确保编辑模式下能正确初始化表单
watch(
  () => props.editData,
  (newEditData) => {
    if (props.editMode && newEditData && visible.value) {
      initFormForEdit()
    }
  },
  { deep: true }
)

// 监听编辑模式变化
watch(
  () => props.editMode,
  (newEditMode) => {
    if (visible.value) {
      if (newEditMode && props.editData) {
        initFormForEdit()
      } else if (!newEditMode) {
        resetForm()
      }
    }
  }
)

watch(visible, (v) => {
  if (!v) emit('close')
})

// 组件挂载后检查是否需要初始化编辑数据
onMounted(() => {
  console.log('SiteAppointment onMounted:', {
    visible: props.visible,
    editMode: props.editMode,
    editData: props.editData
  })

  if (props.visible && props.editMode && props.editData) {
    nextTick(() => {
      initFormForEdit()
    })
  }
})

// 添加一个计算属性来监控表单状态
const formDebugInfo = computed(() => ({
  formValues: form.value,
  editMode: props.editMode,
  editData: props.editData,
  visible: props.visible
}))

// 监听表单调试信息变化
watch(
  formDebugInfo,
  (newInfo) => {
    console.log('表单状态变化:', newInfo)
  },
  { deep: true }
)

// 表单引用和加载状态
const formRef = ref()
const loading = ref(false)
const reservationsLoading = ref(false)

// 从API获取的预约数据
const apiReservations = ref<SiteAppointment[]>([])
// 标记API是否已经被调用过
const apiDataFetched = ref(false)

// 表单数据
const form = ref<{
  activityName: string
  activityType: string
  startDate: string
  endDate: string
  startTime: string
  endTime: string
  peopleCount: number | string
  contactName: string
  contactPhone: string
  status: string
  remark: string
}>({
  activityName: '',
  activityType: '',
  startDate: '',
  endDate: '',
  startTime: '',
  endTime: '',
  peopleCount: '',
  contactName: '',
  contactPhone: '',
  status: '已确认',
  remark: ''
})

const activityTypeOptions = [
  { label: '培训', value: '培训' },
  { label: '考试', value: '考试' },
  { label: '会议', value: '会议' },
  { label: '讲座', value: '讲座' },
  { label: '其他', value: '其他' }
]

// 预约状态选项
const statusOptions = [
  { label: '已确认', value: '已确认' },
  { label: '待确认', value: '待确认' },
  { label: '已取消', value: '已取消' }
]

// 禁用过去的日期
const disabledDate = (time: Date) => {
  const today = new Date()
  today.setHours(0, 0, 0, 0) // 设置为当天的开始时间
  return time.getTime() < today.getTime()
}

// 获取状态标签类型
const getStatusTagType = (status?: string) => {
  switch (status) {
    case '已确认':
      return 'success'
    case '待确认':
      return 'warning'
    case '已取消':
      return 'danger'
    default:
      return 'info'
  }
}

// 自定义验证规则：结束日期不能早于开始日期
const validateEndDate = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('请选择结束日期'))
    return
  }
  if (form.value.startDate && value < form.value.startDate) {
    callback(new Error('结束日期不能早于开始日期'))
    return
  }
  callback()
}

const rules = {
  activityName: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
  activityType: [{ required: true, message: '请选择活动类型', trigger: 'change' }],
  startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
  endDate: [
    { required: true, message: '请选择结束日期', trigger: 'change' },
    { validator: validateEndDate, trigger: 'change' }
  ],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
  peopleCount: [{ required: true, message: '请输入预计人数', trigger: 'blur' }],
  contactName: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
  contactPhone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  status: [{ required: true, message: '请选择预约状态', trigger: 'change' }]
}

// 监听开始日期变化，重新验证结束日期
watch(
  () => form.value.startDate,
  () => {
    if (form.value.endDate && formRef.value) {
      formRef.value.validateField('endDate')
    }
  }
)

// 获取场地预约数据
const fetchSiteReservations = async () => {
  if (!props.siteInfo.id) return

  try {
    reservationsLoading.value = true

    const params: AppointmentListParams = {
      siteId: props.siteInfo.id
    }

    const result = await SiteManagementApi.getAppointmentList(params)
    apiReservations.value = result
    apiDataFetched.value = true // 标记API已被调用

    console.log('获取场地预约数据成功:', {
      siteId: props.siteInfo.id,
      siteName: props.siteInfo.name,
      totalReservations: result.length,
      reservations: result,
      apiDataFetched: true
    })
  } catch (error) {
    console.error('获取场地预约数据失败:', error)
    ElMessage.error('获取场地预约数据失败')
    apiReservations.value = []
    apiDataFetched.value = true // 即使失败也标记为已调用
  } finally {
    reservationsLoading.value = false
  }
}

// 监听场地信息变化，重新获取预约数据
watch(
  () => props.siteInfo.id,
  (newSiteId, oldSiteId) => {
    // 场地ID变化时重置状态
    if (newSiteId !== oldSiteId) {
      apiReservations.value = []
      apiDataFetched.value = false
    }

    if (newSiteId && props.visible) {
      fetchSiteReservations()
    }
  },
  { immediate: true }
)

// 监听弹窗显示状态，获取预约数据
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.siteInfo.id) {
      // 如果还没有获取过数据，则获取
      if (!apiDataFetched.value) {
        fetchSiteReservations()
      }
    } else if (!visible) {
      // 弹窗关闭时重置状态，确保下次打开时重新获取数据
      apiReservations.value = []
      apiDataFetched.value = false
    }
  }
)

// 过滤并排序预约数据：只显示未过期的预约，按时间排序
const filteredReservations = computed(() => {
  // 只有在API数据已获取后才处理数据，否则返回空数组
  if (!apiDataFetched.value) {
    return []
  }

  // 完全依赖API获取的数据，不使用props传递的数据
  const reservationData = apiReservations.value

  console.log('filteredReservations计算:', {
    apiDataFetched: apiDataFetched.value,
    apiReservationsLength: apiReservations.value.length,
    reservationData: reservationData
  })

  if (reservationData.length === 0) {
    return []
  }

  const today = new Date()
  today.setHours(0, 0, 0, 0) // 设置为当天的开始时间

  return reservationData
    .filter((reservation) => {
      // 过滤未过期的预约（endDate >= 当前日期）
      if (!reservation.endDate) return true // 如果没有结束日期，默认显示

      const endDate = new Date(reservation.endDate)
      endDate.setHours(0, 0, 0, 0)

      return endDate.getTime() >= today.getTime()
    })
    .sort((a, b) => {
      // 按开始日期排序，最近的在前
      const dateA = new Date(a.startDate || '').getTime()
      const dateB = new Date(b.startDate || '').getTime()
      return dateA - dateB
    })
})

// 格式化日期范围显示
const formatDateRange = (reservation: SiteAppointment) => {
  const startDate = reservation.startDate
  const endDate = reservation.endDate
  const timeRange = reservation.timeRange || `${reservation.startTime}-${reservation.endTime}`

  if (startDate === endDate) {
    // 同一天的预约
    return `${startDate} ${timeRange}`
  } else {
    // 跨天的预约
    return `${startDate} 至 ${endDate} ${timeRange}`
  }
}

const onCancel = () => {
  emit('close')
}

// 重置表单
const resetForm = () => {
  form.value = {
    activityName: '',
    activityType: '',
    startDate: '',
    endDate: '',
    startTime: '',
    endTime: '',
    peopleCount: '',
    contactName: '',
    contactPhone: '',
    status: '已确认',
    remark: ''
  }
  // 清除表单验证状态
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 编辑模式下初始化表单数据
const initFormForEdit = () => {
  if (!props.editData) {
    console.warn('initFormForEdit: 没有编辑数据')
    return
  }

  const editData = props.editData
  console.log('initFormForEdit: 编辑数据', editData)

  // 使用 nextTick 确保 DOM 已更新
  nextTick(() => {
    form.value = {
      activityName: editData.activityName || '',
      activityType: editData.activityType || '',
      startDate: editData.startDate || '',
      endDate: editData.endDate || '',
      startTime: editData.startTime || '',
      endTime: editData.endTime || '',
      peopleCount: editData.peopleCount || '',
      contactName: editData.contactName || '',
      contactPhone: editData.contactPhone || '',
      status: editData.status || '已确认',
      remark: editData.remark || ''
    }

    console.log('initFormForEdit: 表单数据已设置', form.value)

    // 清除表单验证状态
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  })
}

const onSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    if (props.editMode && props.editData) {
      // 编辑模式：更新预约
      const updateData: UpdateAppointmentParams = {
        id: props.editData.id!,
        siteId: props.siteInfo.id!,
        activityName: form.value.activityName,
        activityType: form.value.activityType,
        startDate: form.value.startDate,
        endDate: form.value.endDate,
        startTime: form.value.startTime,
        endTime: form.value.endTime,
        peopleCount: Number(form.value.peopleCount),
        contactName: form.value.contactName,
        contactPhone: form.value.contactPhone,
        status: form.value.status,
        remark: form.value.remark
      }

      await SiteManagementApi.updateAppointment(updateData)
      ElMessage.success('预约更新成功')
    } else {
      // 新增模式：创建预约
      const appointmentData: CreateAppointmentParams = {
        siteId: props.siteInfo.id!,
        activityName: form.value.activityName,
        activityType: form.value.activityType,
        startDate: form.value.startDate,
        endDate: form.value.endDate,
        startTime: form.value.startTime,
        endTime: form.value.endTime,
        peopleCount: Number(form.value.peopleCount),
        contactName: form.value.contactName,
        contactPhone: form.value.contactPhone,
        status: form.value.status,
        remark: form.value.remark
      }

      await SiteManagementApi.createAppointment(appointmentData)
      ElMessage.success('预约成功')
    }

    // 刷新预约列表
    await fetchSiteReservations()

    // 重置表单
    resetForm()

    emit('submit')
  } catch (error) {
    console.error(props.editMode ? '更新预约失败:' : '预约失败:', error)
    ElMessage.error(props.editMode ? '更新预约失败' : '预约失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.site-appointment-drawer {
  .el-drawer__header {
    font-size: 20px;
    font-weight: bold;
  }
  .el-drawer__body {
    max-height: 80vh;
    overflow-y: auto;
  }
  .site-info-card {
    margin-bottom: 8px;
  }
  .site-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 4px;
  }
  .site-info-row {
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
  }
  .reserved-list {
    margin-top: 18px;
  }
  .reserved-title {
    font-weight: bold;
    margin-bottom: 8px;
  }
  .reserved-card {
    margin-bottom: 8px;
  }
  .reserved-activity {
    font-weight: bold;
    font-size: 15px;
    margin-bottom: 2px;
  }
  .reserved-info {
    color: #555;
    font-size: 13px;
    margin-bottom: 2px;
  }
  .reserved-status {
    float: right;
    margin-top: -24px;
  }
}
@media (max-width: 800px) {
  .site-appointment-drawer .el-drawer {
    width: 98vw !important;
    min-width: 0;
  }
  .el-drawer__header {
    font-size: 16px;
  }
}
</style>
