<template>
  <div class="scheduling-board">
    <!-- 顶部操作区 -->
    <div class="sb-toolbar">
      <div class="sb-toolbar-left">
        <el-button-group>
          <el-button @click="onPrevMonth"
            ><el-icon><i class="el-icon-arrow-left"></i></el-icon> 上月</el-button
          >
          <el-button @click="onToday">回到今日</el-button>
          <el-button @click="onNextMonth"
            >下月 <el-icon><i class="el-icon-arrow-right"></i></el-icon
          ></el-button>
        </el-button-group>
        <el-date-picker
          v-model="currentDate"
          type="month"
          format="YYYY-MM"
          value-format="YYYY-MM"
          :clearable="false"
          style="width: 120px"
        />
      </div>
      <div class="sb-toolbar-center">
        <el-button :type="viewType === 'month' ? 'primary' : 'default'" @click="viewType = 'month'"
          >月视图</el-button
        >
      </div>
      <div class="sb-toolbar-right">
        <el-select v-model="campus" placeholder="全部校区" style="width: 130px; margin-right: 8px">
          <el-option v-for="item in campusOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <el-input v-model="search" placeholder="搜索场地..." style="width: 160px" clearable />
      </div>
    </div>
    <!-- 主内容区 -->
    <div class="sb-main-card">
      <!-- 固定头部区域 -->
      <div class="sb-table-header-row">
        <div class="sb-table-header-left">
          <div class="sb-table-header">场地列表</div>
        </div>
        <div class="sb-table-header-right">
          <!-- 右侧头部容器，支持横向滚动 -->
          <div class="sb-calendar-header-container" ref="calendarHeaderContainer">
            <div class="sb-calendar-header">
              <div v-for="d in days" :key="d" class="sb-calendar-day">{{ d }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 统一滚动容器 -->
      <div class="sb-table-scroll-container">
        <div class="sb-table">
          <div class="sb-table-left">
            <div
              v-for="site in filteredSites"
              :key="site.id"
              class="sb-site-row"
              :class="{ 'sb-site-row--disabled': !isSiteAvailable(site) }"
            >
              <!-- 状态标签 - 右上角 -->
              <el-tag
                :type="getSiteStatusType(site.status)"
                size="small"
                class="sb-site-status sb-site-status--top-right"
              >
                {{ site.status }}
              </el-tag>

              <!-- 场地信息 -->
              <div class="sb-site-content">
                <div class="sb-site-title">
                  {{ site.name }}
                </div>
                <div class="sb-site-desc">
                  {{ site.campus }} · {{ site.type }} · {{ site.seatTotal }}座
                </div>
              </div>

              <!-- 预约按钮 - 右下角 -->
              <el-button
                size="small"
                type="primary"
                class="sb-site-btn sb-site-btn--bottom-right"
                :disabled="!isSiteAvailable(site)"
                @click="onBookSite(site)"
              >
                +预约
              </el-button>
            </div>
          </div>
          <div class="sb-table-right">
            <!-- 右侧内容容器，支持横向滚动 -->
            <div class="sb-calendar-content-container" ref="calendarContentContainer">
              <div
                v-for="site in filteredSites"
                :key="site.id"
                class="sb-calendar-row"
                :class="{ 'sb-calendar-row--disabled': !isSiteAvailable(site) }"
              >
                <div
                  v-for="d in days"
                  :key="d"
                  class="sb-calendar-cell"
                  :class="{ 'sb-calendar-cell--disabled': !isSiteAvailable(site) }"
                  @click="isSiteAvailable(site) ? onCellClick(site, d) : null"
                >
                  <div
                    v-if="isReserved(site.id, d)"
                    class="sb-reserved-block"
                    :class="{ 'sb-reserved-block--disabled': !isSiteAvailable(site) }"
                    :title="isSiteAvailable(site) ? getReservationTitle(site.id, d) : '场地不可用'"
                    @click.stop="isSiteAvailable(site) ? onReservedBlockClick(site, d) : null"
                  >
                    <!-- 预约块填满单元格，无文本显示 -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 预约弹窗 -->
    <SiteAppointment
      v-if="appointmentVisible"
      :visible="appointmentVisible"
      :siteInfo="selectedSite"
      :reservedList="reservedList"
      :editMode="editMode"
      :editData="editData"
      @close="closeAppointment"
      @submit="handleAppointmentSubmit"
    />

    <!-- 预约详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      :title="`${selectedSite?.name} - ${selectedDate} 预约详情`"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="dayReservations.length === 0" class="no-reservations">
        <el-empty description="该日期暂无预约安排" />
        <div style="text-align: center; margin-top: 16px">
          <el-button type="primary" @click="onBookSiteFromDetail">立即预约</el-button>
        </div>
      </div>
      <div v-else>
        <div v-for="reservation in dayReservations" :key="reservation.id" class="reservation-item">
          <el-card shadow="never" style="margin-bottom: 12px">
            <div class="reservation-header">
              <h4>{{ reservation.activity_name }}</h4>
              <div class="reservation-actions">
                <el-tag :type="reservation.status === '已确认' ? 'success' : 'warning'">
                  {{ reservation.status || '已确认' }}
                </el-tag>
                <el-button
                  v-if="hasEditPermission"
                  type="primary"
                  size="small"
                  plain
                  @click="onEditReservation(reservation)"
                  style="margin-left: 8px"
                >
                  编辑
                </el-button>
              </div>
            </div>
            <div class="reservation-details">
              <p><strong>时间：</strong>{{ reservation.time }}</p>
              <p><strong>类型：</strong>{{ reservation.type }}</p>
              <p><strong>人数：</strong>{{ reservation.people_count }}人</p>
              <p
                ><strong>联系人：</strong>{{ reservation.contact_name }} ({{
                  reservation.contact_phone
                }})</p
              >
              <p v-if="reservation.remark"><strong>备注：</strong>{{ reservation.remark }}</p>
            </div>
          </el-card>
        </div>
        <div style="text-align: center; margin-top: 16px">
          <el-button type="primary" @click="onBookSiteFromDetail">继续预约</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import SiteAppointment from './SiteAppointment.vue'
import { ElMessage } from 'element-plus'
import { checkPermi } from '@/utils/permission'
import {
  SiteManagementApi,
  type Site,
  type SiteAppointment as SiteAppointmentType,
  type AppointmentBySiteAndDateParams
} from '@/api/infra/siteManagement'

// 获取当前年月（YYYY-MM）
function getCurrentYearMonth() {
  const now = new Date()
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
}

const currentDate = ref(getCurrentYearMonth())
const viewType = ref('month')
const campus = ref('全部校区')
const search = ref('')

const campusOptions = ['全部校区', '总部校区', '华东分校', '华南分校', '华北分校', '西部分校']

// 横向滚动同步相关的ref
const calendarHeaderContainer = ref<HTMLElement>()
const calendarContentContainer = ref<HTMLElement>()

// 横向滚动同步控制变量
const isHeaderScrolling = ref(false)
const isContentScrolling = ref(false)

// 数据状态
const loading = ref(false)
const sites = ref<Site[]>([])
const appointments = ref<{ [siteId: number]: SiteAppointmentType[] }>({})

const days = ref<number[]>([])
function updateDays() {
  const [year, month] = currentDate.value.split('-').map(Number)
  const lastDay = new Date(year, month, 0).getDate()
  days.value = Array.from({ length: lastDay }, (_, i) => i + 1)
}
updateDays()
watch(currentDate, updateDays)

// 预约数据
const reservations = ref<any[]>([])

// API调用方法
const fetchSites = async () => {
  try {
    loading.value = true
    const params: any = {}

    if (campus.value && campus.value !== '全部校区') {
      params.campusName = campus.value
    }
    if (search.value) {
      params.name = search.value
    }

    const result = await SiteManagementApi.getSiteList(params)
    sites.value = result
  } catch (error) {
    console.error('获取场地列表失败:', error)
    ElMessage.error('获取场地列表失败')
  } finally {
    loading.value = false
  }
}

const fetchAppointments = async () => {
  try {
    const [year, month] = currentDate.value.split('-').map(Number)
    const startDate = `${year}-${month.toString().padStart(2, '0')}-01`
    const lastDay = new Date(year, month, 0).getDate()
    const endDate = `${year}-${month.toString().padStart(2, '0')}-${lastDay.toString().padStart(2, '0')}`

    // 为每个场地获取预约数据
    const appointmentPromises = sites.value.map(async (site) => {
      if (!site.id) return

      const params: AppointmentBySiteAndDateParams = {
        siteId: site.id,
        startDate,
        endDate
      }

      const result = await SiteManagementApi.getAppointmentBySiteAndDate(params)
      appointments.value[site.id] = result
    })

    await Promise.all(appointmentPromises)

    // 转换为reservations格式以兼容现有代码
    const newReservations: any[] = []
    Object.entries(appointments.value).forEach(([siteId, siteAppointments]) => {
      siteAppointments.forEach((appointment) => {
        // 确保日期格式一致性 - 标准化为YYYY-MM-DD格式
        const appointmentDate = appointment.startDate
        let normalizedDate: string

        try {
          // 尝试解析日期并标准化为YYYY-MM-DD格式
          const dateObj = new Date(appointmentDate)
          if (isNaN(dateObj.getTime())) {
            console.warn('无效的日期格式:', appointmentDate)
            normalizedDate = appointmentDate // 保持原格式
          } else {
            const year = dateObj.getFullYear()
            const month = (dateObj.getMonth() + 1).toString().padStart(2, '0')
            const day = dateObj.getDate().toString().padStart(2, '0')
            normalizedDate = `${year}-${month}-${day}`
          }
        } catch (error) {
          console.warn('日期解析失败:', appointmentDate, error)
          normalizedDate = appointmentDate // 保持原格式
        }

        const day = new Date(normalizedDate).getDate()

        newReservations.push({
          id: appointment.id,
          siteId: Number(siteId),
          day,
          activity_name: appointment.activityName,
          date: normalizedDate, // 使用标准化的日期格式
          time: appointment.timeRange || `${appointment.startTime}-${appointment.endTime}`,
          type: appointment.activityType,
          people_count: appointment.peopleCount,
          contact_name: appointment.contactName,
          contact_phone: appointment.contactPhone,
          status: appointment.status,
          remark: appointment.remark
        })
      })
    })

    reservations.value = newReservations

    // 调试信息：验证数据转换结果
    console.log('预约数据转换完成:', {
      totalAppointments: newReservations.length,
      sampleData: newReservations.slice(0, 3),
      dateFormats: [...new Set(newReservations.map((r) => r.date))].slice(0, 5)
    })
  } catch (error) {
    console.error('获取预约数据失败:', error)
    ElMessage.error('获取预约数据失败')
  }
}

// 监听变化，重新获取数据
watch(
  [currentDate, campus, search],
  () => {
    fetchSites()
  },
  { immediate: false }
)

watch(
  sites,
  () => {
    if (sites.value.length > 0) {
      fetchAppointments()
    }
  },
  { deep: true }
)

// 预约相关状态
const appointmentVisible = ref(false)
const selectedSite = ref<any>({})
const reservedList = ref<SiteAppointmentType[]>([])

// 编辑预约相关状态
const editMode = ref(false)
const editData = ref<any>(null)

// 预约详情弹窗相关状态
const detailVisible = ref(false)
const selectedDate = ref('')
const dayReservations = ref<any[]>([])

const filteredSites = computed(() => {
  return sites.value.filter(
    (site) =>
      (campus.value === '全部校区' || site.campus === campus.value) &&
      (!search.value || site.name.includes(search.value))
  )
})

// 权限检查
const hasEditPermission = computed(() => {
  // 检查是否有编辑预约的权限
  return true // checkPermi(['infra:site-appointment:update'])
})

const isReserved = (siteId: number, day: number) => {
  return reservations.value.some((r) => r.siteId === siteId && r.day === day)
}

// 判断场地是否可用（可用或已预约状态）
const isSiteAvailable = (site: any) => {
  return site.status === '可用' || site.status === '已预约'
}

// 获取场地状态标签类型
const getSiteStatusType = (status: string) => {
  switch (status) {
    case '可用':
      return 'success'
    case '已预约':
      return 'warning'
    case '维护中':
      return 'info'
    case '停用':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取预约详情标题（鼠标悬停显示）- 显示该日期所有预约的汇总信息
const getReservationTitle = (siteId: number, day: number) => {
  const dayReservations = reservations.value.filter((r) => r.siteId === siteId && r.day === day)

  if (dayReservations.length === 0) {
    return ''
  }

  // 按时间排序
  const sortedReservations = dayReservations.sort((a, b) => {
    const timeA = a.time.split('-')[0] // 获取开始时间
    const timeB = b.time.split('-')[0]
    return timeA.localeCompare(timeB)
  })

  // 格式化为"活动名称-(时间段)"，多条记录用换行符分隔
  return sortedReservations.map((r) => `${r.activity_name}-(${r.time})`).join('\n')
}

// 点击预约按钮
const onBookSite = (site: any) => {
  // 检查场地状态
  if (!isSiteAvailable(site)) {
    ElMessage.warning(`场地状态为"${site.status}"，无法预约`)
    return
  }

  // 重置编辑状态，确保是新增模式
  editMode.value = false
  editData.value = null

  selectedSite.value = { ...site }
  // SiteAppointment组件现在会主动获取API数据，不需要传递reservedList
  // 为了确保没有mock数据，这里传递空数组
  reservedList.value = []
  appointmentVisible.value = true
}

// 点击日历格子
const onCellClick = (site: any, day: number) => {
  // 检查场地状态
  if (!isSiteAvailable(site)) {
    ElMessage.warning(`场地状态为"${site.status}"，无法查看或预约`)
    return
  }

  const [year, month] = currentDate.value.split('-').map(Number)
  const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`

  // 获取该场地当天的预约
  const dayReservationsList = reservations.value.filter(
    (r) => r.siteId === site.id && r.date === dateStr
  )

  // 根据是否有预约数据决定显示弹窗还是提示消息
  if (dayReservationsList.length > 0) {
    // 有预约：显示预约详情弹窗
    selectedSite.value = { ...site }
    selectedDate.value = dateStr
    dayReservations.value = dayReservationsList
    detailVisible.value = true
  } else {
    // 没有预约：显示提示消息
    ElMessage.info(`${site.name} 在 ${dateStr} 没有预约安排`)
  }
}

// 点击预约块
const onReservedBlockClick = (site: any, day: number) => {
  // 检查场地状态
  if (!isSiteAvailable(site)) {
    ElMessage.warning(`场地状态为"${site.status}"，无法查看预约详情`)
    return
  }

  const [year, month] = currentDate.value.split('-').map(Number)
  const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`

  // 调试信息
  console.log('预约块点击调试信息:', {
    siteId: site.id,
    siteName: site.name,
    day: day,
    dateStr: dateStr,
    currentDate: currentDate.value,
    allReservations: reservations.value,
    reservationsCount: reservations.value.length
  })

  // 获取该场地当天的预约
  const dayReservationsList = reservations.value.filter(
    (r) => r.siteId === site.id && r.date === dateStr
  )

  console.log('筛选结果:', {
    dayReservationsList: dayReservationsList,
    filterCondition: `siteId=${site.id} && date=${dateStr}`,
    matchingReservations: reservations.value.filter((r) => r.siteId === site.id),
    allDatesForSite: reservations.value.filter((r) => r.siteId === site.id).map((r) => r.date)
  })

  // 预约块只在有预约数据时才显示，所以直接显示预约详情弹窗
  if (dayReservationsList.length > 0) {
    selectedSite.value = { ...site }
    selectedDate.value = dateStr
    dayReservations.value = dayReservationsList
    detailVisible.value = true
    console.log('预约详情弹窗已显示')
  } else {
    // 理论上不应该到达这里，因为预约块只在有预约时才显示
    console.error('预约块点击但未找到预约数据，这可能是数据不一致的问题')
    ElMessage.warning(`未找到预约数据。调试信息：场地ID=${site.id}, 日期=${dateStr}`)
  }
}

// 从详情弹窗中预约
const onBookSiteFromDetail = () => {
  detailVisible.value = false
  onBookSite(selectedSite.value)
}

// 编辑预约
const onEditReservation = (reservation: any) => {
  console.log('onEditReservation: 原始预约数据', reservation)

  // 关闭详情弹窗
  detailVisible.value = false

  // 构建编辑数据，将预约详情数据转换为 SiteAppointment 格式
  const timeParts = reservation.time ? reservation.time.split('-') : ['', '']
  const convertedEditData = {
    id: reservation.id,
    siteId: selectedSite.value.id,
    siteName: selectedSite.value.name,
    siteCampusName: selectedSite.value.campusName || selectedSite.value.campus,
    activityName: reservation.activity_name,
    activityType: reservation.type,
    startDate: reservation.date,
    endDate: reservation.date, // 假设是单日预约
    startTime: timeParts[0] ? timeParts[0].trim() : '',
    endTime: timeParts[1] ? timeParts[1].trim() : '',
    timeRange: reservation.time,
    peopleCount: reservation.people_count,
    contactName: reservation.contact_name,
    contactPhone: reservation.contact_phone,
    status: reservation.status || '已确认',
    remark: reservation.remark || ''
  }

  console.log('onEditReservation: 转换后的编辑数据', convertedEditData)

  // 设置编辑模式和数据
  editMode.value = true
  editData.value = convertedEditData
  selectedSite.value = { ...selectedSite.value }
  reservedList.value = []

  console.log('onEditReservation: 设置状态完成', {
    editMode: editMode.value,
    editData: editData.value,
    selectedSite: selectedSite.value
  })

  // 打开预约组件
  appointmentVisible.value = true
}

// 关闭预约弹窗
const closeAppointment = () => {
  appointmentVisible.value = false
  selectedSite.value = {}
  reservedList.value = []
  // 重置编辑状态
  editMode.value = false
  editData.value = null
}

// 处理预约提交
const handleAppointmentSubmit = async () => {
  try {
    // 预约已经在SiteAppointment组件中处理，这里只需要刷新数据
    await fetchAppointments()

    // 关闭预约弹窗
    closeAppointment()

    ElMessage.success('预约成功，排期看板已更新')
  } catch (error) {
    console.error('刷新预约数据失败:', error)
    ElMessage.error('刷新预约数据失败')
  }
}

const onPrevMonth = async () => {
  const [year, month] = currentDate.value.split('-').map(Number)
  let newYear = year
  let newMonth = month - 1
  if (newMonth === 0) {
    newYear -= 1
    newMonth = 12
  }
  currentDate.value = `${newYear}-${String(newMonth).padStart(2, '0')}`
  // 重新获取预约数据
  await fetchAppointments()
}

const onNextMonth = async () => {
  const [year, month] = currentDate.value.split('-').map(Number)
  let newYear = year
  let newMonth = month + 1
  if (newMonth === 13) {
    newYear += 1
    newMonth = 1
  }
  currentDate.value = `${newYear}-${String(newMonth).padStart(2, '0')}`
  // 重新获取预约数据
  await fetchAppointments()
}

// 横向滚动同步方法
const syncHeaderScroll = (scrollLeft: number) => {
  if (calendarHeaderContainer.value && !isHeaderScrolling.value) {
    isContentScrolling.value = true

    // 计算头部容器的最大滚动距离
    const headerMaxScroll =
      calendarHeaderContainer.value.scrollWidth - calendarHeaderContainer.value.clientWidth
    // 限制滚动距离不超过最大值
    const clampedScrollLeft = Math.min(scrollLeft, headerMaxScroll)

    calendarHeaderContainer.value.scrollLeft = clampedScrollLeft
    nextTick(() => {
      isContentScrolling.value = false
    })
  }
}

const syncContentScroll = (scrollLeft: number) => {
  if (calendarContentContainer.value && !isContentScrolling.value) {
    isHeaderScrolling.value = true

    // 计算内容容器的最大滚动距离
    const contentMaxScroll =
      calendarContentContainer.value.scrollWidth - calendarContentContainer.value.clientWidth
    // 限制滚动距离不超过最大值
    const clampedScrollLeft = Math.min(scrollLeft, contentMaxScroll)

    calendarContentContainer.value.scrollLeft = clampedScrollLeft
    nextTick(() => {
      isHeaderScrolling.value = false
    })
  }
}

// 头部横向滚动事件处理
const handleHeaderScroll = (event: Event) => {
  if (isContentScrolling.value) return

  const target = event.target as HTMLElement
  const scrollLeft = target.scrollLeft
  syncContentScroll(scrollLeft)
}

// 内容横向滚动事件处理
const handleContentScroll = (event: Event) => {
  if (isHeaderScrolling.value) return

  const target = event.target as HTMLElement
  const scrollLeft = target.scrollLeft
  syncHeaderScroll(scrollLeft)
}

// 确保两个容器的内容宽度一致
const ensureWidthConsistency = () => {
  if (calendarHeaderContainer.value && calendarContentContainer.value) {
    const headerContent = calendarHeaderContainer.value.querySelector('.sb-calendar-header')
    const contentRows = calendarContentContainer.value.querySelectorAll('.sb-calendar-row')

    if (headerContent && contentRows.length > 0) {
      // 计算头部内容的实际宽度
      const headerWidth = headerContent.scrollWidth

      // 确保所有内容行都有相同的宽度
      contentRows.forEach((row) => {
        const rowElement = row as HTMLElement
        rowElement.style.minWidth = `${headerWidth}px`
      })

      // 同时确保头部也有正确的最小宽度
      const headerElement = headerContent as HTMLElement
      headerElement.style.minWidth = `${headerWidth}px`
    }
  }
}

// 初始化横向滚动监听器
const initHorizontalScrollSync = () => {
  if (calendarHeaderContainer.value) {
    calendarHeaderContainer.value.addEventListener('scroll', handleHeaderScroll, { passive: true })
  }
  if (calendarContentContainer.value) {
    calendarContentContainer.value.addEventListener('scroll', handleContentScroll, {
      passive: true
    })
  }

  // 确保宽度一致性
  ensureWidthConsistency()
}

// 清理横向滚动监听器
const cleanupHorizontalScrollSync = () => {
  if (calendarHeaderContainer.value) {
    calendarHeaderContainer.value.removeEventListener('scroll', handleHeaderScroll)
  }
  if (calendarContentContainer.value) {
    calendarContentContainer.value.removeEventListener('scroll', handleContentScroll)
  }
}

// 监听数据变化，重新确保宽度一致性
watch(
  [currentDate, filteredSites],
  () => {
    nextTick(() => {
      ensureWidthConsistency()
    })
  },
  { deep: true }
)

// 组件挂载时初始化数据和横向滚动同步
onMounted(async () => {
  // 初始化数据
  await fetchSites()

  // 初始化横向滚动同步
  nextTick(() => {
    initHorizontalScrollSync()
  })
})

// 组件卸载时清理监听器
onUnmounted(() => {
  cleanupHorizontalScrollSync()
})

const onToday = () => {
  currentDate.value = getCurrentYearMonth()
}
</script>

<style scoped lang="scss">
.scheduling-board {
  padding: 18px;
  background: #f7f8fa;
}
.sb-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.sb-toolbar-left {
  display: flex;
  align-items: center;
  gap: 8px;
}
.sb-toolbar-center {
  display: flex;
  gap: 8px;
}
.sb-toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}
.sb-main-card {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px #f0f1f2;
  padding: 18px 0 0 0;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  height: 80vh;
}

// 固定头部区域
.sb-table-header-row {
  display: flex;
  flex-shrink: 0;
  border-bottom: 1px solid #e5e6eb;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.sb-table-header-left {
  width: 260px;
  border-right: 1px solid #e5e6eb;
  padding: 0 0 0 18px;
  display: flex;
  align-items: center;
}

.sb-table-header-right {
  flex: 1;
  overflow: hidden; // 防止头部区域产生滚动条
}

// 右侧头部容器 - 支持横向滚动
.sb-calendar-header-container {
  overflow-x: auto;
  overflow-y: hidden;
  padding-left: 18px;

  // 隐藏滚动条但保持滚动功能
  scrollbar-width: none; // Firefox
  -ms-overflow-style: none; // IE/Edge

  &::-webkit-scrollbar {
    display: none; // Chrome/Safari
  }
}

// 统一滚动容器
.sb-table-scroll-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
.sb-table {
  display: flex;
  align-items: stretch;
  min-height: 0;
}

.sb-table-left {
  width: 260px;
  border-right: 1px solid #e5e6eb;
  background: #fff;
  padding: 0 0 0 18px;
}
.sb-table-header {
  font-weight: bold;
  font-size: 16px;
  padding: 12px 0;
  background: #fff;
}
.sb-site-row {
  height: 85px; // 增加行高到85px，提供更舒适的间距
  padding: 12px 0; // 增加内边距
  border-bottom: 1px dashed #f0f0f0;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start; // 确保内容左对齐

  &.sb-site-row--disabled {
    background-color: #f5f5f5;
    opacity: 0.6;

    .sb-site-title {
      color: #999;
    }

    .sb-site-desc {
      color: #bbb;
    }
  }
}

// 场地内容区域
.sb-site-content {
  width: calc(100% - 120px); // 为右侧状态标签和按钮留出空间
  padding-right: 8px;
}

.sb-site-title {
  font-weight: 500;
  font-size: 16px; // 稍微增大字体
  line-height: 1.3;
  margin-bottom: 6px; // 增加与描述的间距
  color: #303133;
}

.sb-site-desc {
  color: #888;
  font-size: 13px;
  margin: 0;
  line-height: 1.4; // 增加行高提升可读性
}

// 状态标签 - 右上角定位
.sb-site-status {
  &.sb-site-status--top-right {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 2;
  }
}

// 预约按钮 - 右下角定位
.sb-site-btn {
  &.sb-site-btn--bottom-right {
    position: absolute;
    right: 8px;
    bottom: 8px;
    font-size: 13px;
    padding: 2px 12px;
    border-radius: 6px;
    z-index: 2;
  }
}
.sb-table-right {
  flex: 1;
  overflow: hidden; // 防止表格区域产生滚动条
}

// 右侧内容容器 - 支持横向滚动
.sb-calendar-content-container {
  overflow-x: auto;
  overflow-y: hidden;
  padding-left: 18px;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
.sb-calendar-header {
  display: flex;
  padding: 12px 0;
  background: #fff;
  width: fit-content; // 使用fit-content确保宽度精确匹配内容
  min-width: 100%; // 确保至少占满容器宽度
}
.sb-calendar-day {
  flex: 1 1 0;
  min-width: 40px; // 与日期格子保持一致的最小宽度
  text-align: center;
  color: #888;
  font-size: 13px;
  padding: 2px 0;
  box-sizing: border-box; // 确保盒模型一致
}
.sb-calendar-row {
  display: flex;
  height: 85px; // 与左侧场地行高度保持一致
  flex: none; // 改为固定高度，不使用flex伸缩
  align-items: center; // 确保内容垂直居中对齐
  width: fit-content; // 使用fit-content确保宽度精确匹配内容
  min-width: 100%; // 确保至少占满容器宽度

  &.sb-calendar-row--disabled {
    background-color: #f9f9f9;
    opacity: 0.6;
  }
}
.sb-calendar-cell {
  flex: 1 1 0;
  min-width: 40px; // 与日期头部保持一致的最小宽度
  height: 85px; // 与行高保持一致
  border-right: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  cursor: pointer;
  transition: background-color 0.2s;
  box-sizing: border-box; // 确保盒模型一致
  position: relative; // 为预约块的绝对定位提供定位上下文

  &:hover {
    background-color: #f8f9fa;
  }

  &.sb-calendar-cell--disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}
.sb-reserved-block {
  width: 100%; // 填满整个日历格子宽度
  height: 100%; // 填满整个日历格子高度
  background: #e57373;
  border-radius: 4px; // 稍微减小圆角，适应填满效果
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0; // 隐藏文本，因为不再显示文字
  font-weight: 500;
  text-align: center;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s;
  position: absolute; // 绝对定位，确保填满父容器
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  &:hover {
    background: #d32f2f;
    transform: none; // 移除缩放效果，避免影响布局
    opacity: 0.9; // 使用透明度变化作为hover效果
  }

  &.sb-reserved-block--disabled {
    background: #ccc;
    color: #999;
    cursor: not-allowed;

    &:hover {
      background: #ccc;
      opacity: 1;
      transform: none;
    }
  }
}

// 预约详情弹窗样式
.reservation-item {
  .reservation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    h4 {
      margin: 0;
      color: #303133;
      font-size: 16px;
      flex: 1;
    }

    .reservation-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .reservation-details {
    p {
      margin: 8px 0;
      color: #606266;
      font-size: 14px;

      strong {
        color: #303133;
        margin-right: 8px;
      }
    }
  }
}

.no-reservations {
  text-align: center;
  padding: 20px;
}

// 超大屏幕 (>1400px) - 提供最舒适的行高
@media (min-width: 1400px) {
  .sb-site-row,
  .sb-calendar-row,
  .sb-calendar-cell {
    height: 95px; // 超大屏幕使用更大的行高
  }

  .sb-site-row {
    padding: 15px 0; // 增加内边距
  }

  .sb-site-title {
    font-size: 17px; // 增大字体
    margin-bottom: 8px;
  }

  .sb-site-desc {
    font-size: 14px; // 增大字体
  }

  .sb-reserved-block {
    // 预约块填满格子，无需设置高度
    border-radius: 6px; // 超大屏幕使用稍大的圆角
  }

  .sb-calendar-day,
  .sb-calendar-cell {
    min-width: 45px; // 增加最小宽度，保持一致
  }
}

@media (max-width: 1200px) {
  .sb-main-card {
    height: auto;
    min-height: 400px;
  }
  .sb-table-left {
    width: 180px;
  }
  .sb-calendar-day,
  .sb-calendar-cell {
    min-width: 28px; // 稍微增加最小宽度
  }

  // 保持行高一致 - 中等屏幕使用70px
  .sb-site-row,
  .sb-calendar-row,
  .sb-calendar-cell {
    height: 70px;
  }

  .sb-site-row {
    padding: 10px 0; // 调整内边距
  }

  .sb-site-title {
    font-size: 15px;
    margin-bottom: 5px;
  }

  .sb-reserved-block {
    // 预约块填满格子，无需设置高度
    border-radius: 3px; // 中等屏幕使用中等圆角
  }

  // 中等屏幕的布局调整
  .sb-site-content {
    width: calc(100% - 100px); // 调整内容区域宽度
  }

  .sb-site-status--top-right {
    top: 6px;
    right: 6px;
  }

  .sb-site-btn--bottom-right {
    right: 6px;
    bottom: 6px;
    font-size: 12px;
    padding: 1px 10px;
  }
}

@media (max-width: 900px) {
  .sb-main-card {
    height: auto;
    min-height: 300px;
  }
  .sb-table-left {
    width: 120px;
  }
  .sb-calendar-day,
  .sb-calendar-cell {
    min-width: 22px; // 稍微增加最小宽度
    font-size: 11px;
  }

  // 保持行高一致 - 小屏幕使用60px，仍然比原来的45px更舒适
  .sb-site-row,
  .sb-calendar-row,
  .sb-calendar-cell {
    height: 60px;
  }

  .sb-site-row {
    padding: 8px 0; // 调整内边距
  }

  .sb-site-title {
    font-size: 14px; // 稍微增大字体
    margin-bottom: 4px;
  }

  .sb-site-desc {
    font-size: 12px; // 稍微增大字体
  }

  .sb-site-btn {
    font-size: 11px;
    padding: 2px 8px; // 稍微增加内边距
  }

  .sb-reserved-block {
    // 预约块填满格子，无需设置高度
    border-radius: 2px; // 小屏幕使用小圆角
  }

  // 小屏幕的布局调整
  .sb-site-content {
    width: calc(100% - 80px); // 进一步调整内容区域宽度
  }

  .sb-site-status--top-right {
    top: 4px;
    right: 4px;
    transform: scale(0.9); // 稍微缩小标签
  }

  .sb-site-btn--bottom-right {
    right: 4px;
    bottom: 4px;
    font-size: 10px;
    padding: 1px 6px;
    transform: scale(0.9); // 稍微缩小按钮
  }
}
</style>
