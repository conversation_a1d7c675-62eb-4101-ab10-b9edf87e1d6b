# 排期看板预约编辑功能实现说明

## 功能概述

在排期看板的预约详情弹窗中，为每条预约记录添加了编辑按钮，允许用户修改预约信息。

## 实现的功能

### 1. 编辑按钮
- 在每个预约记录卡片的右上角添加了"编辑"按钮
- 按钮样式与现有UI风格保持一致，使用 Element Plus 的 el-button 组件
- 按钮位置不影响现有的预约信息展示布局

### 2. 编辑功能支持的字段
- 活动名称 (activityName)
- 活动类型 (activityType) 
- 开始日期 (startDate)
- 结束日期 (endDate)
- 开始时间 (startTime)
- 结束时间 (endTime)
- 人数 (peopleCount)
- 联系人信息 (contactName, contactPhone)
- 预约状态 (status)
- 备注 (remark)

### 3. 权限控制
- 使用 `checkPermi(['infra:site-appointment:update'])` 检查编辑权限
- 只有有权限的用户才能看到和使用编辑功能
- 编辑按钮通过 `v-if="hasEditPermission"` 进行权限控制

## 技术实现

### 1. API 接口扩展
在 `src/api/infra/siteManagement/index.ts` 中添加了：
- `UpdateAppointmentParams` 接口类型定义
- `updateAppointment` API 方法

```typescript
// 更新预约请求参数
export interface UpdateAppointmentParams {
  id: number
  siteId: number
  activityName: string
  activityType: string
  startDate: string
  endDate: string
  startTime: string
  endTime: string
  peopleCount: number
  contactName: string
  contactPhone: string
  status?: string
  remark?: string
}

// 更新预约
updateAppointment: async (data: UpdateAppointmentParams): Promise<boolean> => {
  return await request.put({ url: '/publicbiz/site-management/appointment/update', data })
}
```

### 2. SiteAppointment 组件扩展
在 `src/views/infra/ResourceCenter/SiteManagement/components/SiteAppointment.vue` 中：

#### 新增 Props
```typescript
const props = defineProps<{
  visible: boolean
  siteInfo: Site
  reservedList?: SiteAppointment[]
  editMode?: boolean      // 新增：编辑模式标识
  editData?: SiteAppointment  // 新增：编辑数据
}>()
```

#### 新增方法
- `resetForm()`: 重置表单数据
- `initFormForEdit()`: 编辑模式下初始化表单数据

#### 修改的功能
- 抽屉标题根据编辑模式动态显示
- 提交按钮文本根据编辑模式显示"保存修改"或"立即预约"
- 提交方法支持新增和编辑两种模式

### 3. SchedulingBoard 组件扩展
在 `src/views/infra/ResourceCenter/SiteManagement/components/SchedulingBoard.vue` 中：

#### 新增状态管理
```typescript
// 编辑预约相关状态
const editMode = ref(false)
const editData = ref<any>(null)

// 权限检查
const hasEditPermission = computed(() => {
  return checkPermi(['infra:site-appointment:update'])
})
```

#### 新增方法
- `onEditReservation(reservation)`: 处理编辑预约的逻辑

#### 修改的UI
- 预约记录卡片的头部布局调整，支持编辑按钮
- 编辑按钮添加权限控制

## 使用流程

1. 用户在排期看板中点击某个日期的预约块
2. 弹出预约详情弹窗，显示该日期的所有预约记录
3. 每条预约记录右上角显示"编辑"按钮（需要权限）
4. 点击编辑按钮，关闭详情弹窗，打开预约编辑表单
5. 表单中预填充当前预约的信息
6. 用户修改信息后点击"保存修改"
7. 调用更新API，成功后刷新数据并关闭表单

## 样式调整

在预约详情弹窗的样式中添加了：
```scss
.reservation-header {
  .reservation-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}
```

## 注意事项

1. **API 接口**: 需要后端实现 `PUT /publicbiz/site-management/appointment/update` 接口
2. **权限配置**: 需要在权限系统中配置 `infra:site-appointment:update` 权限
3. **数据转换**: 编辑时需要将预约详情数据格式转换为 SiteAppointment 格式
4. **状态管理**: 编辑完成后需要重置编辑状态，避免状态污染

## 后续优化建议

1. 可以考虑添加删除预约功能
2. 可以添加预约状态变更的快捷操作
3. 可以优化编辑表单的用户体验，如字段验证提示等
4. 可以添加编辑操作的操作日志记录
